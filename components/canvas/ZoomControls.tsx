import React, { useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusIcon, MinusIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface ZoomControlsProps {
	viewport: {
		x: number;
		y: number;
		scale: number;
	};
	setViewport: (viewport: { x: number; y: number; scale: number }) => void;
	canvasSize: {
		width: number;
		height: number;
	};
	images?: Array<{ x: number; y: number; width: number; height: number }>;
	onZoomFunctionsReady?: (functions: { handleZoomIn: () => void; handleZoomOut: () => void; handleZoomToFit: () => void }) => void;
}

export const ZoomControls: React.FC<ZoomControlsProps> = ({ viewport, setViewport, canvasSize, images = [], onZoomFunctionsReady }) => {
	const handleZoomIn = useCallback(() => {
		const newScale = Math.min(5, viewport.scale * 1.2);
		const centerX = canvasSize.width / 2;
		const centerY = canvasSize.height / 2;

		// Zoom towards center
		const mousePointTo = {
			x: (centerX - viewport.x) / viewport.scale,
			y: (centerY - viewport.y) / viewport.scale,
		};

		setViewport({
			x: centerX - mousePointTo.x * newScale,
			y: centerY - mousePointTo.y * newScale,
			scale: newScale,
		});
	}, [viewport, canvasSize, setViewport]);

	const handleZoomOut = useCallback(() => {
		const newScale = Math.max(0.1, viewport.scale / 1.2);
		const centerX = canvasSize.width / 2;
		const centerY = canvasSize.height / 2;

		// Zoom towards center
		const mousePointTo = {
			x: (centerX - viewport.x) / viewport.scale,
			y: (centerY - viewport.y) / viewport.scale,
		};

		setViewport({
			x: centerX - mousePointTo.x * newScale,
			y: centerY - mousePointTo.y * newScale,
			scale: newScale,
		});
	}, [viewport, canvasSize, setViewport]);

	const handleResetView = () => {
		setViewport({ x: 0, y: 0, scale: 1 });
	};

	const handleZoomToFit = useCallback(() => {
		if (images.length === 0) {
			// If no images, reset to default view
			setViewport({ x: 0, y: 0, scale: 1 });
			return;
		}

		// Calculate bounding box of all images
		let minX = Infinity,
			minY = Infinity;
		let maxX = -Infinity,
			maxY = -Infinity;

		images.forEach((img) => {
			minX = Math.min(minX, img.x);
			minY = Math.min(minY, img.y);
			maxX = Math.max(maxX, img.x + img.width);
			maxY = Math.max(maxY, img.y + img.height);
		});

		const contentWidth = maxX - minX;
		const contentHeight = maxY - minY;
		const contentCenterX = minX + contentWidth / 2;
		const contentCenterY = minY + contentHeight / 2;

		// Add some padding (10% of canvas size)
		const padding = Math.min(canvasSize.width, canvasSize.height) * 0.1;
		const availableWidth = canvasSize.width - padding * 2;
		const availableHeight = canvasSize.height - padding * 2;

		// Calculate scale to fit content
		const scaleX = availableWidth / contentWidth;
		const scaleY = availableHeight / contentHeight;
		const scale = Math.min(scaleX, scaleY, 5); // Cap at 5x zoom

		// Calculate position to center content
		const x = canvasSize.width / 2 - contentCenterX * scale;
		const y = canvasSize.height / 2 - contentCenterY * scale;

		setViewport({ x, y, scale });
	}, [images, canvasSize, setViewport]);

	// Expose zoom functions to parent component
	useEffect(() => {
		if (onZoomFunctionsReady) {
			onZoomFunctionsReady({
				handleZoomIn,
				handleZoomOut,
				handleZoomToFit,
			});
		}
	}, [handleZoomIn, handleZoomOut, handleZoomToFit, onZoomFunctionsReady]);

	return (
		<div className="absolute bottom-4 left-4 z-20 hidden flex-col items-start gap-4 md:flex">
			<div className={cn("bg-card flex flex-row items-center gap-1 overflow-clip rounded-full px-2 py-1")}>
				<Button variant="ghost" size="icon" onClick={handleZoomIn} className="size-6 rounded-full p-0">
					<PlusIcon className="size-3.5" />
				</Button>
				<p className="text-center text-[11px]">{Math.round(viewport.scale * 100)}%</p>
				<Button variant="ghost" size="sm" onClick={handleZoomOut} className="size-6 rounded-full p-0">
					<MinusIcon className="size-3.5" />
				</Button>
				{/* <Button variant="ghost" size="sm" onClick={handleResetView} className="h-10 w-10 rounded-none p-0" title="Reset view">
					<Maximize2 className="h-4 w-4" />
				</Button> */}
			</div>
		</div>
	);
};
