import type { PlacedImage, GenerationSettings, ActiveGeneration } from "@/@types/canvas";
import type { FalClient } from "@fal-ai/client";
import { toast } from "sonner";

interface GenerationHandlerDeps {
	images: PlacedImage[];
	selectedIds: string[];
	generationSettings: GenerationSettings;
	customApiKey?: string;
	canvasSize: { width: number; height: number };
	viewport: { x: number; y: number; scale: number };
	falClient: FalClient;
	setImages: React.Dispatch<React.SetStateAction<PlacedImage[]>>;
	setSelectedIds: React.Dispatch<React.SetStateAction<string[]>>;
	setActiveGenerations: React.Dispatch<React.SetStateAction<Map<string, ActiveGeneration>>>;
	setIsGenerating: React.Dispatch<React.SetStateAction<boolean>>;
	generateTextToImage: (params: any) => Promise<any>;
	saveToHistory: () => void;
}

export const uploadImageDirect = async (dataUrl: string, falClient: FalClient) => {
	// Convert data URL to blob first
	const response = await fetch(dataUrl);
	const blob = await response.blob();

	try {
		// Check size before attempting upload
		if (blob.size > 10 * 1024 * 1024) {
			// 10MB warning
			console.warn("Large image detected:", (blob.size / 1024 / 1024).toFixed(2) + "MB");
		}

		// Upload directly to FAL through proxy (using the client instance)
		const uploadResult = await falClient.storage.upload(blob);

		return { url: uploadResult };
	} catch (error: any) {
		// Check for rate limit error
		const isRateLimit =
			error.status === 429 || error.message?.includes("429") || error.message?.includes("rate limit") || error.message?.includes("Rate limit");

		if (isRateLimit) {
			toast.error("Rate limit exceeded", {
				description: "Add your FAL API key to bypass rate limits. Without an API key, uploads are limited.",
			});
		} else {
			toast.error("Failed to upload image", {
				description: error instanceof Error ? error.message : "Unknown error",
			});
		}

		// Re-throw the error so calling code knows upload failed
		throw error;
	}
};

export const generateImage = (
	imageUrl: string,
	x: number,
	y: number,
	groupId: string,
	generationSettings: GenerationSettings,
	setImages: GenerationHandlerDeps["setImages"],
	setActiveGenerations: GenerationHandlerDeps["setActiveGenerations"],
) => {
	// Create placeholder first
	const placeholderId = `generated-${Date.now()}`;
	const placeholderSize = 800; // 正方形占位符尺寸

	setImages((prev) => [
		...prev,
		{
			id: placeholderId,
			src: "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
			x,
			y,
			width: placeholderSize,
			height: placeholderSize,
			rotation: 0,
			isGenerated: true,
			generationStatus: "generating",
			parentGroupId: groupId,
		},
	]);

	// Store generation params
	setActiveGenerations((prev) =>
		new Map(prev).set(placeholderId, {
			imageUrl,
			prompt: generationSettings.prompt,
		}),
	);
};

export const handleRun = async (deps: GenerationHandlerDeps) => {
	const {
		images,
		selectedIds,
		generationSettings,
		customApiKey,
		canvasSize,
		viewport,
		falClient,
		setImages,
		setSelectedIds,
		setActiveGenerations,
		setIsGenerating,
		generateTextToImage,
		saveToHistory,
	} = deps;

	if (!generationSettings.prompt) {
		toast.error("No Prompt", {
			description: "Please enter a prompt to generate an image",
		});
		return;
	}

	setIsGenerating(true);
	const selectedImages = images.filter((img) => selectedIds.includes(img.id));

	// If no images are selected, do text-to-image generation
	if (selectedImages.length === 0) {
		// Place at center of viewport
		const viewportCenterX = (canvasSize.width / 2 - viewport.x) / viewport.scale;
		const viewportCenterY = (canvasSize.height / 2 - viewport.y) / viewport.scale;

		// Create placeholder first
		const placeholderId = `generated-${Date.now()}-${Math.random()}`;
		const placeholderSize = 800; // 正方形占位符尺寸

		setImages((prev) => [
			...prev,
			{
				id: placeholderId,
				src: "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
				x: viewportCenterX - placeholderSize / 2,
				y: viewportCenterY - placeholderSize / 2,
				width: placeholderSize,
				height: placeholderSize,
				rotation: 0,
				isGenerated: true,
				generationStatus: "generating",
			},
		]);

		try {
			const result = await generateTextToImage({
				prompt: generationSettings.prompt,
				imageSize: "square",
			});

			// Load the generated image to get actual dimensions
			const img = new window.Image();
			img.crossOrigin = "anonymous";

			img.onload = () => {
				// Update the placeholder with actual image and real dimensions
				setImages((prev) =>
					prev.map((imgItem) =>
						imgItem.id === placeholderId
							? {
									...imgItem,
									src: result.url,
									width: img.width,
									height: img.height,
									uploadStatus: "uploaded",
									ossUrl: result.url,
									generationStatus: "completed",
								}
							: imgItem,
					),
				);
			};

			img.onerror = () => {
				// If image loading fails, use dimensions from API result
				setImages((prev) =>
					prev.map((imgItem) =>
						imgItem.id === placeholderId
							? {
									...imgItem,
									src: result.url,
									width: result.width,
									height: result.height,
									uploadStatus: "uploaded",
									ossUrl: result.url,
									generationStatus: "completed",
								}
							: imgItem,
					),
				);
			};

			img.src = result.url;

			// Select the new image
			setSelectedIds([placeholderId]);
		} catch (error) {
			console.error("Error generating image:", error);
			// Remove the failed placeholder
			setImages((prev) => prev.filter((img) => img.id !== placeholderId));

			// Save to history after removing failed generation
			setTimeout(() => {
				saveToHistory();
			}, 50);

			toast.error("Generation failed", {
				description: error instanceof Error ? error.message : "Failed to generate image",
			});
		} finally {
			setIsGenerating(false);
		}
		return;
	}

	// Process each selected image individually for image-to-image
	let successCount = 0;
	let failureCount = 0;

	for (const img of selectedImages) {
		try {
			// Get crop values
			const cropX = img.cropX || 0;
			const cropY = img.cropY || 0;
			const cropWidth = img.cropWidth || 1;
			const cropHeight = img.cropHeight || 1;

			// Load the image
			const imgElement = new window.Image();
			imgElement.crossOrigin = "anonymous"; // Enable CORS
			imgElement.src = img.src;
			await new Promise((resolve) => {
				imgElement.onload = resolve;
			});

			// Create a canvas for the image at original resolution
			const canvas = document.createElement("canvas");
			const ctx = canvas.getContext("2d");
			if (!ctx) throw new Error("Failed to get canvas context");

			// Calculate the effective original dimensions accounting for crops
			let effectiveWidth = imgElement.naturalWidth;
			let effectiveHeight = imgElement.naturalHeight;

			if (cropWidth !== 1 || cropHeight !== 1) {
				effectiveWidth = cropWidth * imgElement.naturalWidth;
				effectiveHeight = cropHeight * imgElement.naturalHeight;
			}

			// Set canvas size to the original resolution (not display size)
			canvas.width = effectiveWidth;
			canvas.height = effectiveHeight;

			console.log(`Processing image at ${canvas.width}x${canvas.height} (original res, display: ${img.width}x${img.height})`);

			// Always use the crop values (default to full image if not set)
			ctx.drawImage(
				imgElement,
				cropX * imgElement.naturalWidth,
				cropY * imgElement.naturalHeight,
				cropWidth * imgElement.naturalWidth,
				cropHeight * imgElement.naturalHeight,
				0,
				0,
				canvas.width,
				canvas.height,
			);

			// Convert to blob and upload
			const blob = await new Promise<Blob>((resolve) => {
				canvas.toBlob((blob) => resolve(blob!), "image/png");
			});

			const reader = new FileReader();
			const dataUrl = await new Promise<string>((resolve) => {
				reader.onload = (e) => resolve(e.target?.result as string);
				reader.readAsDataURL(blob);
			});

			let uploadResult;
			try {
				uploadResult = await uploadImageDirect(dataUrl, falClient);
			} catch (uploadError) {
				console.error("Failed to upload image:", uploadError);
				failureCount++;
				// Skip this image if upload fails
				continue;
			}

			// Only proceed with generation if upload succeeded
			if (!uploadResult?.url) {
				console.error("Upload succeeded but no URL returned");
				failureCount++;
				continue;
			}

			// Calculate output size maintaining aspect ratio
			const aspectRatio = canvas.width / canvas.height;
			const baseSize = 512;
			let outputWidth = baseSize;
			let outputHeight = baseSize;

			if (aspectRatio > 1) {
				outputHeight = Math.round(baseSize / aspectRatio);
			} else {
				outputWidth = Math.round(baseSize * aspectRatio);
			}

			const groupId = `single-${Date.now()}-${Math.random()}`;
			generateImage(uploadResult.url, img.x + img.width + 20, img.y, groupId, generationSettings, setImages, setActiveGenerations);
			successCount++;
		} catch (error) {
			console.error("Error processing image:", error);
			failureCount++;
			toast.error("Failed to process image", {
				description: error instanceof Error ? error.message : "Failed to process image",
			});
		}
	}

	// Done processing all images
	setIsGenerating(false);
};
